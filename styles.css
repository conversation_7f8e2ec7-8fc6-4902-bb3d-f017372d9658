/* Ghost Sync Plugin Styles */

/* New Post Display Styles */
.ghost-sync-new-post {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: var(--background-secondary);
  border: 1px solid var(--background-modifier-border);
  border-radius: 8px;
  margin-bottom: 16px;
}

.ghost-sync-new-post-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.ghost-sync-new-post-content {
  flex: 1;
}

.ghost-sync-new-post-content h3 {
  margin: 0 0 8px 0;
  color: var(--text-normal);
  font-size: 16px;
}

.ghost-sync-new-post-content p {
  margin: 0 0 12px 0;
  color: var(--text-muted);
  font-size: 14px;
}

.ghost-sync-new-post-details {
  background: var(--background-primary);
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 12px;
  font-size: 13px;
}

.ghost-sync-new-post-details div {
  margin-bottom: 4px;
}

.ghost-sync-new-post-details div:last-child {
  margin-bottom: 0;
}

.ghost-sync-new-post-hint {
  font-style: italic;
  color: var(--text-accent);
  font-size: 13px;
}

/* Buttons in new post display */
.ghost-sync-new-post+.ghost-sync-buttons {
  margin-top: 16px;
}

.ghost-sync-modal {
  padding: 20px;
}

.ghost-sync-modal h2 {
  margin-bottom: 15px;
}

.ghost-sync-modal input {
  width: 100%;
  margin-bottom: 15px;
  padding: 8px;
  border: 1px solid var(--background-modifier-border);
  border-radius: 4px;
  background: var(--background-primary);
  color: var(--text-normal);
}

.ghost-sync-modal button {
  padding: 8px 16px;
  background: var(--interactive-accent);
  color: var(--text-on-accent);
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.ghost-sync-modal button:hover {
  background: var(--interactive-accent-hover);
}

/* Post Selection Modal Styles */
.ghost-post-suggestion {
  padding: 8px 12px;
  border-bottom: 1px solid var(--background-modifier-border);
}

.ghost-post-suggestion:last-child {
  border-bottom: none;
}

.ghost-post-title {
  font-weight: 500;
  color: var(--text-normal);
  margin-bottom: 4px;
}

.ghost-post-meta {
  font-size: 0.85em;
  color: var(--text-muted);
  opacity: 0.8;
}

.ghost-post-suggestion:hover .ghost-post-title {
  color: var(--text-accent);
}

.ghost-post-suggestion:hover .ghost-post-meta {
  opacity: 1;
}

/* Ghost Sync Status View Styles */

.ghost-sync-status-view {
  padding: 16px;
  font-family: var(--font-interface);
}

.ghost-sync-header {
  margin-bottom: 16px;
  border-bottom: 1px solid var(--background-modifier-border);
  padding-bottom: 8px;
}

.ghost-sync-header h3 {
  margin: 0;
  color: var(--text-normal);
  font-size: 16px;
  font-weight: 600;
}

.ghost-sync-no-file,
.ghost-sync-not-article {
  color: var(--text-muted);
  font-style: italic;
  text-align: center;
  padding: 20px;
}

.ghost-sync-file-info {
  margin-bottom: 16px;
  padding: 12px;
  background-color: var(--background-secondary);
  border-radius: 6px;
}

.ghost-sync-file-info strong {
  color: var(--text-normal);
  display: block;
  margin-bottom: 4px;
}

.ghost-sync-filename {
  color: var(--text-accent);
  font-family: var(--font-monospace);
  font-size: 14px;
}

/* Feature Image Preview */
.ghost-sync-feature-image-preview {
  margin-bottom: 16px;
  text-align: center;
}

.ghost-sync-feature-image {
  max-width: 100%;
  max-height: 120px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid var(--background-modifier-border);
}

/* Compact Status List */
.ghost-sync-status-list {
  margin-bottom: 16px;
}

.ghost-sync-compact-item {
  display: flex;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid var(--background-modifier-border-hover);
}

.ghost-sync-compact-item:last-child {
  border-bottom: none;
}

.ghost-sync-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  font-size: 12px;
  font-weight: bold;
  margin-right: 8px;
  flex-shrink: 0;
}

.ghost-sync-badge-synced {
  background-color: var(--color-green);
  color: white;
}

.ghost-sync-badge-different {
  background-color: var(--color-red);
  color: white;
}

.ghost-sync-badge-unknown {
  background-color: var(--text-muted);
  color: white;
}

.ghost-sync-badge-new_post {
  background-color: var(--color-blue);
  color: white;
}

.ghost-sync-compact-content {
  flex: 1;
  font-size: 13px;
  line-height: 1.4;
}

.ghost-sync-compact-content strong {
  color: var(--text-normal);
}

.ghost-sync-value {
  color: var(--text-muted);
  font-family: var(--font-monospace);
  font-size: 12px;
}

/* Tags Container */
.ghost-sync-tags-container {
  display: inline-flex;
  align-items: center;
  flex-wrap: wrap;
}

.ghost-sync-expand-tags-btn {
  margin-left: 4px;
  font-size: 10px;
  padding: 1px 4px;
  border: 1px solid var(--background-modifier-border);
  border-radius: 2px;
  background: var(--background-secondary);
  color: var(--text-muted);
  cursor: pointer;
  transition: background-color 0.2s;
}

.ghost-sync-expand-tags-btn:hover {
  background: var(--background-modifier-hover);
}

.ghost-sync-ghost-info {
  margin-bottom: 20px;
  padding: 12px;
  background-color: var(--background-primary-alt);
  border-radius: 6px;
  border: 1px solid var(--background-modifier-border);
}

.ghost-sync-ghost-info h4 {
  margin: 0 0 12px 0;
  color: var(--text-normal);
  font-size: 14px;
  font-weight: 600;
}

.ghost-sync-info-grid {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.ghost-sync-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.ghost-sync-info-label {
  color: var(--text-muted);
  font-size: 12px;
}

.ghost-sync-info-value {
  color: var(--text-normal);
  font-size: 12px;
  font-weight: 500;
}

.ghost-sync-controls {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--background-modifier-border);
}

.ghost-sync-btn {
  padding: 8px 16px;
  border: 1px solid var(--background-modifier-border);
  border-radius: 4px;
  background-color: var(--background-secondary);
  color: var(--text-normal);
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.ghost-sync-btn:hover {
  background-color: var(--background-modifier-hover);
  border-color: var(--background-modifier-border-hover);
}

.ghost-sync-btn:active {
  background-color: var(--background-modifier-active);
}

.ghost-sync-btn.mod-cta {
  background-color: var(--interactive-accent);
  color: var(--text-on-accent);
  border-color: var(--interactive-accent);
}

.ghost-sync-btn.mod-cta:hover {
  background-color: var(--interactive-accent-hover);
  border-color: var(--interactive-accent-hover);
}

/* Sync and Refresh Row */
.ghost-sync-row {
  display: flex;
  gap: 8px;
  width: 100%;
}

.ghost-sync-btn-half {
  flex: 1;
}

/* Delete button */
.ghost-delete-btn {
  background-color: var(--color-red) !important;
  color: white !important;
  border-color: var(--color-red) !important;
}

.ghost-delete-btn:hover {
  background-color: var(--color-red) !important;
  opacity: 0.8;
}

/* Mobile responsive */
@media (max-width: 400px) {
  .ghost-sync-status-view {
    padding: 12px;
  }

  .ghost-sync-controls {
    flex-direction: column;
  }

  .ghost-sync-btn {
    width: 100%;
    text-align: center;
  }

  .ghost-sync-compact-content {
    font-size: 12px;
  }

  .ghost-sync-value {
    font-size: 11px;
  }
}

/* Publish Controls Styles */
.ghost-sync-separator {
  margin: 16px 0;
  border: none;
  border-top: 1px solid var(--background-modifier-border);
}

.ghost-sync-publish-header {
  margin-bottom: 12px;
}

.ghost-sync-section-title {
  margin: 0;
  color: var(--text-normal);
  font-size: 14px;
  font-weight: 600;
}

.ghost-sync-newsletter-section,
.ghost-sync-segment-section,
.ghost-sync-test-mode-section {
  margin-bottom: 12px;
}

.ghost-sync-label {
  display: block;
  color: var(--text-normal);
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 4px;
}

.ghost-sync-select {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid var(--background-modifier-border);
  border-radius: 4px;
  background-color: var(--background-primary);
  color: var(--text-normal);
  font-size: 12px;
}

.ghost-sync-select:focus {
  outline: none;
  border-color: var(--interactive-accent);
}

.ghost-sync-checkbox-label {
  display: flex;
  align-items: center;
  color: var(--text-normal);
  font-size: 12px;
  cursor: pointer;
}

.ghost-sync-checkbox {
  margin-right: 6px;
  accent-color: var(--interactive-accent);
}

/* Publish buttons container */
.ghost-sync-publish-buttons {
  display: flex;
  gap: 6px;
  margin-top: 8px;
}

.ghost-sync-publish-btn,
.ghost-sync-send-btn,
.ghost-sync-publish-send-btn {
  flex: 1;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 600;
  border: 1px solid var(--background-modifier-border);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.ghost-sync-publish-btn {
  background-color: var(--background-secondary);
  color: var(--text-normal);
}

.ghost-sync-publish-btn:hover {
  background-color: var(--background-modifier-hover);
}

.ghost-sync-send-btn {
  background-color: var(--color-orange);
  color: white;
  border-color: var(--color-orange);
}

.ghost-sync-send-btn:hover {
  background-color: var(--color-orange);
  opacity: 0.8;
}

.ghost-sync-publish-send-btn {
  background-color: var(--interactive-accent);
  color: var(--text-on-accent);
  border-color: var(--interactive-accent);
}

.ghost-sync-publish-send-btn:hover {
  background-color: var(--interactive-accent-hover);
  border-color: var(--interactive-accent-hover);
}

/* Compact Publish Confirmation Modal Styles */
.publish-confirm-summary {
  margin-bottom: 16px;
  padding: 12px;
  border-radius: 4px;
  background-color: var(--background-secondary);
  border: 1px solid var(--background-modifier-border);
}

.publish-confirm-summary p {
  margin: 4px 0;
  font-size: 14px;
  line-height: 1.4;
}

.test-mode-notice {
  color: var(--color-blue);
  font-weight: 500;
}

.live-mode-warning {
  color: var(--color-orange);
  font-weight: 500;
}

/* Enhanced Modal Styling with Data Attributes */
[data-modal-type] {
  /* Base modal styling that applies to all modal types */
}

[data-modal-type="create-post"] .ghost-sync-modal {
  min-width: 400px;
}

[data-modal-type="post-selection"] .suggester-container {
  min-width: 500px;
}

[data-modal-type="publish-dialog"] .modal-content {
  max-width: 600px;
}

[data-modal-type="post-browser"] .modal-content {
  max-width: 700px;
  max-height: 80vh;
}

/* Enhanced button styling with data attributes */
[data-action="submit"],
[data-action="confirm"] {
  background-color: var(--interactive-accent);
  color: var(--text-on-accent);
}

[data-action="submit"]:hover,
[data-action="confirm"]:hover {
  background-color: var(--interactive-accent-hover);
}

[data-action="cancel"],
[data-action="close"] {
  background-color: var(--background-secondary);
  color: var(--text-normal);
}

[data-action="cancel"]:hover,
[data-action="close"]:hover {
  background-color: var(--background-modifier-hover);
}

/* Enhanced input styling with data attributes */
[data-input] {
  border: 1px solid var(--background-modifier-border);
  border-radius: 4px;
  padding: 8px 12px;
  background: var(--background-primary);
  color: var(--text-normal);
  font-size: 14px;
}

[data-input]:focus {
  border-color: var(--interactive-accent);
  outline: none;
  box-shadow: 0 0 0 2px var(--interactive-accent-hover);
}

/* Post selection styling */
[data-post-slug] {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

[data-post-slug]:hover {
  background-color: var(--background-modifier-hover);
}

.email-only-notice {
  color: var(--color-purple);
  font-weight: 500;
  font-style: italic;
}

/* Legacy styles for backward compatibility */
.publish-confirm-post-info,
.publish-confirm-newsletter-info,
.publish-confirm-recipient-info,
.publish-confirm-test-mode,
.publish-confirm-live-warning,
.publish-confirm-action-summary {
  margin-bottom: 20px;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid var(--background-modifier-border);
}

.publish-confirm-post-info {
  background-color: var(--background-secondary);
}

.publish-confirm-newsletter-info {
  background-color: var(--background-primary-alt);
}

.publish-confirm-recipient-info {
  background-color: var(--background-secondary);
}

.publish-confirm-test-mode {
  background-color: var(--color-blue-rgb);
  background-color: rgba(var(--color-blue-rgb), 0.1);
  border-color: var(--color-blue);
}

.publish-confirm-live-warning {
  background-color: var(--color-red-rgb);
  background-color: rgba(var(--color-red-rgb), 0.1);
  border-color: var(--color-red);
}

.publish-confirm-action-summary {
  background-color: var(--background-primary-alt);
}

.publish-confirm-details p {
  margin: 8px 0;
  font-size: 14px;
  line-height: 1.4;
}

.publish-confirm-details strong {
  color: var(--text-normal);
  font-weight: 600;
}

.publish-confirm-email-list {
  margin: 8px 0 8px 20px;
  padding: 0;
}

.publish-confirm-email-list li {
  margin: 4px 0;
  font-family: var(--font-monospace);
  font-size: 13px;
  color: var(--text-muted);
}

.publish-confirm-more-recipients {
  font-style: italic;
  color: var(--text-muted);
  margin-top: 8px;
}

.publish-confirm-warning {
  padding: 12px;
  background-color: rgba(var(--color-orange-rgb), 0.1);
  border: 1px solid var(--color-orange);
  border-radius: 4px;
  margin-top: 8px;
}

.publish-confirm-warning p {
  margin: 0;
  color: var(--color-orange);
  font-weight: 500;
}

.publish-confirm-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid var(--background-modifier-border);
}

.publish-confirm-buttons button {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.publish-confirm-buttons .mod-cancel {
  background-color: var(--background-secondary);
  color: var(--text-normal);
  border: 1px solid var(--background-modifier-border);
}

.publish-confirm-buttons .mod-cancel:hover {
  background-color: var(--background-modifier-hover);
}

.publish-confirm-buttons .mod-cta {
  background-color: var(--interactive-accent);
  color: var(--text-on-accent);
  border: 1px solid var(--interactive-accent);
}

.publish-confirm-buttons .mod-cta:hover {
  background-color: var(--interactive-accent-hover);
}

/* New UI Styles */

/* Publish Options Section */
.ghost-sync-publish-section {
  margin-bottom: 20px;
  padding: 16px;
  background: var(--background-secondary);
  border-radius: 8px;
  border: 1px solid var(--background-modifier-border);
}

.ghost-sync-section-header {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-normal);
}

.ghost-sync-option-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.ghost-sync-option-row:last-child {
  margin-bottom: 0;
}

.ghost-sync-label {
  min-width: 80px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-normal);
}

.ghost-sync-select {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid var(--background-modifier-border);
  border-radius: 6px;
  background: var(--background-primary);
  color: var(--text-normal);
  font-size: 14px;
}

.ghost-sync-checkbox {
  margin-right: 8px;
}

/* Smart Sync Section */
.ghost-sync-sync-section {
  margin-bottom: 20px;
}

/* Property Sync Section */
.ghost-sync-property-section {
  margin-bottom: 20px;
}

.ghost-sync-property-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: var(--background-secondary);
  border-radius: 6px;
  border: 1px solid var(--background-modifier-border);
  cursor: pointer;
}

.ghost-sync-property-header:hover {
  background: var(--background-modifier-hover);
}

.ghost-sync-toggle-btn {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 12px;
  cursor: pointer;
  padding: 0;
  width: 16px;
  text-align: center;
  flex-shrink: 0;
}

.ghost-sync-status-badge-container {
  flex: 1;
  display: flex;
  justify-content: center;
}

.ghost-sync-status-badge {
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
  min-width: 80px;
}

.ghost-sync-status-badge-success {
  background: var(--color-green);
  color: white;
}

.ghost-sync-status-badge-warning {
  background: var(--color-orange);
  color: white;
}

.ghost-sync-status-badge-unknown {
  background: var(--text-muted);
  color: white;
}

.ghost-sync-status-badge-new {
  background: var(--color-blue);
  color: white;
}

.ghost-sync-property-section .ghost-sync-btn {
  margin-top: 12px;
}

/* Fix status list to be contained within the section */
.ghost-sync-property-section .ghost-sync-status-list {
  margin-top: 12px;
  padding: 12px;
  background: var(--background-primary);
  border-radius: 6px;
  border: 1px solid var(--background-modifier-border);
}

/* Simplified button layout */
.ghost-sync-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--background-modifier-border);
}

.ghost-sync-publish-btn {
  width: 100%;
}

/* Publish Dialog */
.ghost-publish-dialog {
  max-width: 500px;
  padding: 20px;
}

.ghost-publish-dialog h2 {
  margin: 0 0 20px 0;
  color: var(--text-normal);
}

.publish-dialog-section {
  margin-bottom: 20px;
}

.publish-dialog-section h3 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-normal);
}

.publish-dialog-option {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 12px;
  border: 1px solid var(--background-modifier-border);
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
}

.publish-dialog-option:hover {
  background: var(--background-modifier-hover);
}

.publish-dialog-option:last-child {
  margin-bottom: 0;
}

.publish-dialog-option-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.publish-dialog-option-disabled:hover {
  background: var(--background-secondary) !important;
}

.publish-dialog-radio {
  margin-top: 2px;
}

.publish-dialog-radio:disabled {
  cursor: not-allowed;
}

.publish-dialog-label {
  flex: 1;
  cursor: pointer;
}

.publish-dialog-label strong {
  display: block;
  color: var(--text-normal);
  margin-bottom: 4px;
}

.publish-dialog-desc {
  font-size: 12px;
  color: var(--text-muted);
}

.publish-dialog-desc-disabled {
  color: var(--text-faint);
  font-style: italic;
}

.publish-dialog-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.publish-dialog-row:last-child {
  margin-bottom: 0;
}

.publish-dialog-row label {
  min-width: 80px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-normal);
}

.publish-dialog-select {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid var(--background-modifier-border);
  border-radius: 6px;
  background: var(--background-primary);
  color: var(--text-normal);
  font-size: 14px;
  line-height: 1.4;
  min-height: 40px;
}

.publish-dialog-checkbox {
  margin-right: 8px;
}

.publish-dialog-buttons {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 20px;
}

.publish-dialog-buttons button {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}
